#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار إعداد أداة فحص حسابات Shahid
Test script for Shahid account checker setup
"""

import os
import sys
import platform
import subprocess

def test_python_version():
    """اختبار إصدار Python"""
    print("🔍 فحص إصدار Python...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - مدعوم")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - غير مدعوم (يتطلب 3.7+)")
        return False

def test_chrome_installation():
    """اختبار تثبيت Chrome"""
    print("\n🔍 فحص تثبيت Google Chrome...")
    
    if platform.system() == "Windows":
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                try:
                    result = subprocess.run([path, '--version'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        print(f"✅ Chrome موجود: {version}")
                        return True
                except:
                    continue
    else:
        commands = ['google-chrome', 'google-chrome-stable', 'chromium-browser', 'chromium']
        for cmd in commands:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    print(f"✅ Chrome موجود: {version}")
                    return True
            except:
                continue
    
    print("❌ Chrome غير موجود أو لا يمكن الوصول إليه")
    return False

def get_tesseract_path_from_code():
    """استخراج مسار Tesseract من ملف shahid.py"""
    try:
        with open('shahid.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # ابحث عن السطر الذي يحتوي على tesseract_cmd
        import re
        match = re.search(r'pytesseract\.pytesseract\.tesseract_cmd\s*=\s*r?"([^"]+)"', content)
        if match:
            return match.group(1)
        else:
            return r"C:\Program Files\Tesseract-OCR\tesseract.exe"  # القيمة الافتراضية
    except:
        return r"C:\Program Files\Tesseract-OCR\tesseract.exe"  # القيمة الافتراضية

def test_tesseract():
    """اختبار تثبيت Tesseract"""
    print("\n🔍 فحص تثبيت Tesseract OCR...")

    # استخدم نفس المسار المحدد في shahid.py
    tesseract_path_from_code = get_tesseract_path_from_code()

    print(f"🔍 فحص المسار المحدد في الكود: {tesseract_path_from_code}")

    if os.path.exists(tesseract_path_from_code):
        try:
            result = subprocess.run([tesseract_path_from_code, '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.split('\n')[0]
                print(f"✅ Tesseract موجود في المسار المحدد: {version}")
                return True
        except Exception as e:
            print(f"❌ خطأ في تشغيل Tesseract: {e}")
    else:
        print(f"❌ Tesseract غير موجود في المسار المحدد: {tesseract_path_from_code}")

    # جرب من PATH كبديل
    print("🔍 جاري البحث في PATH...")
    try:
        result = subprocess.run(['tesseract', '--version'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version = result.stdout.split('\n')[0]
            print(f"⚠️  Tesseract موجود في PATH: {version}")
            print(f"   💡 لكن الكود يستخدم المسار: {tesseract_path_from_code}")
            print("   💡 قد تحتاج لتحديث المسار في shahid.py السطر 20")
            return False  # نعتبره فشل لأن المسار في الكود مختلف
    except:
        pass

    print("❌ Tesseract غير موجود")
    print("   💡 حمله من: https://github.com/UB-Mannheim/tesseract/wiki")
    print(f"   💡 وتأكد من تثبيته في: {tesseract_path_from_code}")
    return False

def test_pytesseract_integration():
    """اختبار تكامل pytesseract مع نفس إعدادات الكود"""
    print("\n🔍 فحص تكامل pytesseract...")

    try:
        import pytesseract
        from PIL import Image

        # استخدم نفس المسار من الكود
        tesseract_path = get_tesseract_path_from_code()
        pytesseract.pytesseract.tesseract_cmd = tesseract_path

        print(f"🔍 تعيين مسار Tesseract: {tesseract_path}")

        # إنشاء صورة اختبار بسيطة
        from PIL import Image, ImageDraw

        # إنشاء صورة بيضاء مع نص
        img = Image.new('RGB', (200, 50), color='white')
        draw = ImageDraw.Draw(img)

        try:
            # جرب استخدام خط افتراضي
            draw.text((10, 10), "TEST123", fill='black')
        except:
            # إذا فشل، استخدم الخط الافتراضي
            draw.text((10, 10), "TEST123", fill='black')

        # اختبار OCR
        text = pytesseract.image_to_string(img, config='--psm 8')
        text = text.strip()

        if text:
            print(f"✅ pytesseract يعمل بشكل صحيح. النص المستخرج: '{text}'")
            return True
        else:
            print("⚠️  pytesseract يعمل لكن لم يستخرج نص من الصورة الاختبارية")
            return True  # لا يزال يعتبر نجاح لأن المكتبة تعمل

    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار pytesseract: {e}")
        print("   💡 تأكد من صحة مسار Tesseract في shahid.py")
        return False

def test_python_packages():
    """اختبار المكتبات المطلوبة"""
    print("\n🔍 فحص المكتبات المطلوبة...")
    
    required_packages = [
        'selenium',
        'requests', 
        'PIL',
        'pytesseract'
    ]
    
    optional_packages = [
        'webdriver_manager'
    ]
    
    all_good = True
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - مثبت")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            all_good = False
    
    print("\n📦 المكتبات الاختيارية:")
    for package in optional_packages:
        try:
            __import__(package)
            print(f"✅ {package} - مثبت")
        except ImportError:
            print(f"⚠️  {package} - غير مثبت (اختياري)")
    
    return all_good

def test_chromedriver_setup():
    """اختبار إعداد ChromeDriver"""
    print("\n🔍 فحص إعداد ChromeDriver...")
    
    try:
        # استيراد الدوال من الملف الرئيسي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from shahid import setup_chrome_driver, verify_chromedriver
        
        driver_path = setup_chrome_driver()
        if driver_path:
            is_valid, message = verify_chromedriver(driver_path)
            if is_valid:
                print(f"✅ ChromeDriver جاهز: {message}")
                return True
            else:
                print(f"❌ ChromeDriver غير صالح: {message}")
                return False
        else:
            print("❌ فشل في إعداد ChromeDriver")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار ChromeDriver: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار إعداد أداة فحص حسابات Shahid")
    print("=" * 50)
    
    tests = [
        ("Python", test_python_version),
        ("Chrome", test_chrome_installation),
        ("Tesseract", test_tesseract),
        ("Python Packages", test_python_packages),
        ("Pytesseract Integration", test_pytesseract_integration),
        ("ChromeDriver", test_chromedriver_setup)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ نجح" if passed else "❌ فشل"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 جميع الاختبارات نجحت! الأداة جاهزة للاستخدام.")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى إصلاح المشاكل قبل الاستخدام.")
        print("\n💡 نصائح:")
        print("- ثبت المتطلبات: pip install -r requirements.txt")
        print("- تأكد من تثبيت Chrome و Tesseract")
        print("- راجع ملف README.md للمزيد من التفاصيل")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
