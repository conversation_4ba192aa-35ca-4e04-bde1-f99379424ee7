#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة اختبار من أداة فحص حسابات Shahid
Test version of Shahid account checker
"""

import os
import sys

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, verify_chromedriver

def test_chromedriver_only():
    """اختبار ChromeDriver فقط بدون تشغيل المتصفح"""
    print("🔍 اختبار إعداد ChromeDriver...")
    
    driver_path = setup_chrome_driver()
    if not driver_path:
        print("❌ فشل في إعداد ChromeDriver")
        return False
    
    is_valid, message = verify_chromedriver(driver_path)
    if is_valid:
        print(f"✅ {message}")
        return True
    else:
        print(f"❌ {message}")
        return False

def test_selenium_basic():
    """اختبار Selenium الأساسي"""
    print("\n🔍 اختبار Selenium الأساسي...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # تشغيل بدون واجهة
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("🌐 الانتقال إلى صفحة اختبار...")
        driver.get("https://www.google.com")
        
        title = driver.title
        print(f"📄 عنوان الصفحة: {title}")
        
        driver.quit()
        print("✅ اختبار Selenium نجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Selenium: {e}")
        return False

def test_shahid_page_access():
    """اختبار الوصول إلى صفحة Shahid"""
    print("\n🔍 اختبار الوصول إلى صفحة Shahid...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # تشغيل بدون واجهة
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("🌐 الانتقال إلى صفحة Shahid...")
        driver.get("https://shahid.mbc.net")
        
        title = driver.title
        print(f"📄 عنوان الصفحة: {title}")
        
        # تحقق من وجود عناصر الصفحة
        page_source = driver.page_source.lower()
        if "shahid" in page_source or "شاهد" in page_source:
            print("✅ تم الوصول إلى صفحة Shahid بنجاح!")
            success = True
        else:
            print("⚠️  تم الوصول إلى الصفحة لكن المحتوى غير متوقع")
            success = False
        
        driver.quit()
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الوصول إلى صفحة Shahid: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار مكونات أداة فحص حسابات Shahid")
    print("=" * 60)
    
    tests = [
        ("ChromeDriver Setup", test_chromedriver_only),
        ("Selenium Basic", test_selenium_basic),
        ("Shahid Page Access", test_shahid_page_access)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ نجح" if passed else "❌ فشل"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 جميع الاختبارات نجحت! الأداة جاهزة للاستخدام.")
        print("💡 يمكنك الآن تشغيل: python shahid.py")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
