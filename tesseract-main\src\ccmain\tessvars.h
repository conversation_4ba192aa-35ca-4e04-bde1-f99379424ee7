/**********************************************************************
 * File:        tessvars.h  (Formerly tessvars.h)
 * Description: Variables and other globals for tessedit.
 * Author:      <PERSON>
 * Created:     Mon Apr 13 13:13:23 BST 1992
 *
 * (C) Copyright 1992, Hewlett-Packard Ltd.
 ** Licensed under the Apache License, Version 2.0 (the "License");
 ** you may not use this file except in compliance with the License.
 ** You may obtain a copy of the License at
 ** http://www.apache.org/licenses/LICENSE-2.0
 ** Unless required by applicable law or agreed to in writing, software
 ** distributed under the License is distributed on an "AS IS" BASIS,
 ** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ** See the License for the specific language governing permissions and
 ** limitations under the License.
 *
 **********************************************************************/

#ifndef TESSVARS_H
#define TESSVARS_H

#include <cstdio>

extern FILE *debug_fp; // write debug stuff here

#endif
