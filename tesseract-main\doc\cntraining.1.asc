CNTRAINING(1)
=============

NAME
----
cntraining - character normalization training for Tesseract

SYNOPSIS
--------
*cntraining* [-D 'dir'] 'FILE'...

DESCRIPTION
-----------
cntraining takes a list of .tr files, from which it generates the
*normproto* data file (the character normalization sensitivity
prototypes).

OPTIONS
--------
-D 'dir'::
	Directory to write output files to.

SEE ALSO
--------
tesseract(1), shapeclustering(1), mftraining(1)

<https://tesseract-ocr.github.io/tessdoc/Training-Tesseract.html>

COPYING
-------
Copyright (c) Hewlett-Packard Company, 1988
Licensed under the Apache License, Version 2.0

AUTHOR
------
The Tesseract OCR engine was written by <PERSON> and his research groups
at Hewlett Packard (1985-1995) and Google (2006-2018).
