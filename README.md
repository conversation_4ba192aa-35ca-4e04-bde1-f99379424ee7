# أداة فحص حسابات Shahid

أداة Python لفحص صحة حسابات موقع Shahid (شاهد) التابع لـ MBC بشكل تلقائي.

## الميزات

- ✅ **تحميل ChromeDriver تلقائياً** باستخدام Chrome for Testing API الجديد
- ✅ **دعم أنظمة تشغيل متعددة** (Windows, macOS, Linux)
- ✅ **حل الكابتشا النصية** باستخدام OCR
- ✅ **دعم reCAPTCHA** (يطلب التدخل اليدوي)
- ✅ **واجهة رسومية بسيطة** لاختيار ملف الحسابات
- ✅ **حفظ النتائج** في ملفات منفصلة (good.txt و bad.txt)
- ✅ **التحقق من توافق ChromeDriver** مع إصدار Chrome المثبت

## المتطلبات

### 1. Python 3.7 أو أحدث
### 2. Google Chrome متصفح
### 3. Tesseract OCR

#### تثبيت Tesseract OCR:

**Windows:**
- حمل من: https://github.com/UB-Mannheim/tesseract/wiki
- ثبت في المسار الافتراضي: `C:\Program Files\Tesseract-OCR\`

**Ubuntu/Debian:**
```bash
sudo apt-get install tesseract-ocr
```

**macOS:**
```bash
brew install tesseract
```

## التثبيت

1. **استنسخ أو حمل الملفات:**
```bash
git clone <repository-url>
cd shahid-checker
```

2. **ثبت المتطلبات:**
```bash
pip install -r requirements.txt
```

3. **اختبر الإعداد:**
```bash
# على Windows
run_test.bat

# أو باستخدام Python مباشرة
python test_setup.py
```

## الاستخدام

1. **تحضير ملف الحسابات:**
   - أنشئ ملف نصي (.txt) يحتوي على الحسابات
   - كل سطر يجب أن يكون بالصيغة: `email:password`
   - مثال:
     ```
     <EMAIL>:password123
     <EMAIL>:mypassword
     ```

2. **تشغيل الأداة:**
```bash
python shahid.py
```

3. **اختيار الملف:**
   - ستظهر نافذة لاختيار ملف الحسابات
   - اختر الملف النصي الذي يحتوي على الحسابات

4. **انتظار النتائج:**
   - ستبدأ الأداة في فحص الحسابات تلقائياً
   - إذا ظهرت reCAPTCHA، ستحتاج لحلها يدوياً
   - النتائج ستُحفظ في:
     - `good.txt`: الحسابات الصالحة
     - `bad.txt`: الحسابات غير الصالحة مع سبب الفشل

## إدارة ChromeDriver

الأداة تدعم عدة طرق لإدارة ChromeDriver:

1. **التحميل التلقائي** (الطريقة المفضلة)
2. **webdriver-manager** (إذا كان مثبتاً)
3. **النسخ من النظام** (إذا كان ChromeDriver في PATH)
4. **التحميل اليدوي** كحل أخير

### إذا واجهت مشاكل مع ChromeDriver:

1. **ثبت webdriver-manager:**
```bash
pip install webdriver-manager
```

2. **أو حمل ChromeDriver يدوياً:**
   - اذهب إلى: https://googlechromelabs.github.io/chrome-for-testing/
   - حمل الإصدار المتوافق مع Chrome
   - ضع الملف في نفس مجلد السكريبت

## استكشاف الأخطاء

### مشكلة Tesseract:
```
TesseractNotFoundError
```
**الحل:** تأكد من تثبيت Tesseract وتحديث المسار في السطر 20 من الكود.

### مشكلة ChromeDriver:
```
فشل في إعداد ChromeDriver
```
**الحل:** 
- تأكد من تثبيت Chrome
- جرب تثبيت webdriver-manager
- أو حمل ChromeDriver يدوياً

### مشكلة الشبكة:
```
خطأ في الشبكة أثناء تحميل ChromeDriver
```
**الحل:** تحقق من اتصال الإنترنت أو استخدم VPN.

## ملاحظات مهمة

- ⚠️ **استخدم الأداة بمسؤولية** واحترم شروط الخدمة
- ⚠️ **لا تفرط في الاستخدام** لتجنب حظر IP
- ⚠️ **احم بياناتك** ولا تشارك ملفات الحسابات
- ⚠️ **استخدم VPN** إذا لزم الأمر

## الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من إصدارات Python و Chrome
3. جرب تشغيل الأداة كمدير (Administrator)

## الترخيص

هذه الأداة للأغراض التعليمية فقط. استخدمها بمسؤولية.
