# متطلبات مشروع فحص حسابات Shahid
# Python Requirements for Shahid Account Checker

# Selenium for web automation
selenium>=4.15.0

# Image processing for CAPTCHA solving
Pillow>=10.0.0

# OCR for CAPTCHA recognition
pytesseract>=0.3.10

# HTTP requests
requests>=2.31.0

# Optional: WebDriver Manager for automatic ChromeDriver management
# يمكن تثبيته لتسهيل إدارة ChromeDriver تلقائياً
webdriver-manager>=4.0.0

# Note: You also need to install Tesseract OCR separately
# ملاحظة: تحتاج أيضاً لتثبيت Tesseract OCR بشكل منفصل
# 
# For Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
# For Ubuntu/Debian: sudo apt-get install tesseract-ocr
# For macOS: brew install tesseract
