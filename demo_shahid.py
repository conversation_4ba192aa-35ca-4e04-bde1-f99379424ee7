#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة تجريبية من أداة فحص حسابات Shahid
Demo version of Shahid account checker
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, login_shahid

def demo_login_test():
    """تجربة تسجيل الدخول التجريبي"""
    print("🔍 تجربة تسجيل الدخول التجريبي...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # اختبار بيانات وهمية
        test_accounts = [
            ("<EMAIL>", "password123"),
            ("<EMAIL>", "demo123")
        ]
        
        results = []
        
        for username, password in test_accounts:
            print(f"\n🔍 اختبار الحساب: {username}")
            
            try:
                status = login_shahid(driver, username, password)
                print(f"📊 النتيجة: {status}")
                results.append((username, status))
                
                # انتظار قصير بين المحاولات
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ خطأ في اختبار {username}: {e}")
                results.append((username, "error"))
        
        driver.quit()
        
        print("\n" + "="*50)
        print("📊 ملخص نتائج الاختبار:")
        print("="*50)
        
        for username, status in results:
            print(f"{username}: {status}")
        
        print("="*50)
        print("✅ انتهى الاختبار التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار التجريبي: {e}")
        return False

def demo_file_processing():
    """تجربة معالجة ملف الحسابات"""
    print("\n🔍 تجربة معالجة ملف الحسابات...")
    
    # تحقق من وجود ملف الاختبار
    test_file = "test_accounts.txt"
    if not os.path.exists(test_file):
        print(f"❌ ملف الاختبار غير موجود: {test_file}")
        return False
    
    try:
        with open(test_file, encoding="utf-8", errors="ignore") as f:
            lines = [l.strip() for l in f if l.strip()]
        
        print(f"📄 تم قراءة {len(lines)} سطر من الملف")
        
        good, bad = [], []
        
        for i, line in enumerate(lines, 1):
            if ":" not in line:
                bad.append(f"{line} => format_error")
                print(f"❌ [{i}] خطأ في التنسيق: {line}")
                continue
            
            try:
                username, password = line.split(":", 1)
                print(f"✅ [{i}] تنسيق صحيح: {username}")
                
                # محاكاة نتيجة الفحص (بدون تسجيل دخول فعلي)
                if "test" in username.lower() or "demo" in username.lower():
                    status = "simulated_fail"  # محاكاة فشل
                    bad.append(f"{username}:{password} => {status}")
                else:
                    status = "simulated_unknown"  # محاكاة حالة غير معروفة
                    bad.append(f"{username}:{password} => {status}")
                
                print(f"📊 [{i}] النتيجة المحاكاة: {status}")
                
            except Exception as e:
                print(f"❌ [{i}] خطأ في معالجة السطر: {line} - {e}")
                bad.append(f"{line} => processing_error")
        
        # حفظ النتائج
        try:
            with open("demo_good.txt", "w", encoding="utf-8") as g:
                g.write("\n".join(good))
            with open("demo_bad.txt", "w", encoding="utf-8") as b:
                b.write("\n".join(bad))
            
            print(f"\n📊 النتائج:")
            print(f"✅ صالح: {len(good)}")
            print(f"❌ غير صالح: {len(bad)}")
            print("💾 تم حفظ النتائج في demo_good.txt و demo_bad.txt")
            
            return True
            
        except Exception as e:
            print(f"❌ فشل في حفظ النتائج: {e}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🎭 تجربة أداة فحص حسابات Shahid")
    print("=" * 60)
    
    print("⚠️  هذه نسخة تجريبية للاختبار فقط")
    print("⚠️  لن يتم تسجيل دخول فعلي للحسابات")
    print()
    
    # اختبار معالجة الملف
    demo_file_processing()
    
    # اختبار تسجيل الدخول (اختياري)
    print("\n" + "="*60)
    response = input("هل تريد اختبار تسجيل الدخول الفعلي؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        print("⚠️  سيتم فتح المتصفح واختبار تسجيل الدخول...")
        print("⚠️  قد يستغرق هذا بعض الوقت...")
        demo_login_test()
    else:
        print("✅ تم تخطي اختبار تسجيل الدخول")
    
    print("\n" + "="*60)
    print("🎉 انتهت التجربة!")
    print("💡 للاستخدام الفعلي، شغل: python shahid.py")
    print("="*60)

if __name__ == "__main__":
    main()
