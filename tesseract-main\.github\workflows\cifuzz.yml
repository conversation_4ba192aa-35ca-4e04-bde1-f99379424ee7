name: CIFuzz
# OSS-Fuzz CI
# See https://google.github.io/oss-fuzz/getting-started/continuous-integration/
on:
  pull_request:
    branches:
    - main
    paths:
    - '**.cpp'
    - '**.h'
jobs:
  Fuzzing:
    runs-on: ubuntu-latest
    steps:
    - name: Build Fuzzers
      id: build
      uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@master
      with:
        oss-fuzz-project-name: 'tesseract-ocr'
        language: c++
        dry-run: false
    - name: Run Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@master
      with:
        oss-fuzz-project-name: 'tesseract-ocr'
        fuzz-seconds: 600
        dry-run: false
    - name: Upload Crash
      uses: actions/upload-artifact@v4
      if: failure() && steps.build.outcome == 'success'
      with:
        name: artifacts
        path: ./out/artifacts
