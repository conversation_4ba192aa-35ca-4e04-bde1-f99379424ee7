# Based on https://github.com/zdenop/tesserocr/actions/runs/691257659/workflow
# Build Tesseract on Windows using cmake. No Training Tools.
name: cmake-win64
on:
  #push:
  schedule:
    - cron: 0 5 * * *
  workflow_dispatch:

env:
  ILOC: d:/a/local
  png_ver: 1644

jobs:
  build:
    name: cmake-win64
    runs-on: windows-latest
    steps:
      - uses: ilammy/setup-nasm@v1
      - uses: microsoft/setup-msbuild@v2
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          submodules: recursive
      - run: git fetch --prune --unshallow --tags

      - name: Get the version
        id: get_version
        continue-on-error: true
        run: |
             $git_info=$(git describe --tags HEAD)
             $stamp=$(date +'%Y-%m-%d_%H%M%S')
             echo "version=${git_info}" >> $env:GITHUB_OUTPUT
             echo "stamp=${stamp}" >> $env:GITHUB_OUTPUT

      - name: Setup Installation Location
        run: |
             mkdir ${{env.ILOC}}

      #- name: Uninstall Perl
      #  run: |
      #    choco uninstall strawberryperl

      - name: Build and Install zlib-ng
        shell: cmd
        run: |
             git clone --depth 1 https://github.com/zlib-ng/zlib-ng.git
             cd zlib-ng
             cmake -Bbuild -DCMAKE_PREFIX_PATH=${{env.ILOC}} -DCMAKE_INSTALL_PREFIX=${{env.ILOC}} -DBUILD_SHARED_LIBS=OFF -DZLIB_COMPAT=ON -DZLIB_ENABLE_TESTS=OFF -DINSTALL_UTILS=OFF
             cmake --build build --target install
             cd ..

      - name: Build and Install libpng
        shell: cmd
        run: |
             curl -sSL -o lpng${{env.png_ver}}.zip https://download.sourceforge.net/libpng/lpng${{env.png_ver}}.zip
             unzip.exe  -qq lpng${{env.png_ver}}.zip
             cd lpng${{env.png_ver}}
             cmake -Bbuild -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_PREFIX_PATH=${{env.ILOC}} -DCMAKE_INSTALL_PREFIX=${{env.ILOC}} -DPNG_TESTS=OFF -DPNG_SHARED=OFF
             cmake --build build --target install
             cd ..

      - name: Build and Install libjpeg
        shell: cmd
        run: |
             git clone --depth 1 https://github.com/libjpeg-turbo/libjpeg-turbo.git
             cd libjpeg-turbo
             cmake -Bbuild -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_PREFIX_PATH=${{env.ILOC}} -DCMAKE_INSTALL_PREFIX=${{env.ILOC}} -DWITH_TURBOJPEG=OFF -DENABLE_SHARED=OFF
             cmake --build build --target install
             cd ..

      - name: Build and Install jbigkit
        shell: cmd
        run: |
             git clone --depth 1 https://github.com/zdenop/jbigkit.git
             cd jbigkit
             cmake -Bbuild -DCMAKE_PREFIX_PATH=${{env.ILOC}} -DCMAKE_INSTALL_PREFIX=${{env.ILOC}} -DBUILD_PROGRAMS=OFF -DBUILD_TOOLS=OFF -DCMAKE_WARN_DEPRECATED=OFF
             cmake --build build --target install
             cd ..

      - name: Build and Install libtiff
        shell: cmd
        run: |
             git clone -c advice.detachedHead=false -b "v4.6.0" --depth 1 https://gitlab.com/libtiff/libtiff.git
             cd libtiff
             cmake -Bbuild -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_PREFIX_PATH=${{env.ILOC}} -DCMAKE_INSTALL_PREFIX=${{env.ILOC}} -Dtiff-tools=OFF -Dtiff-tests=OFF -Dtiff-contrib=OFF -Dtiff-docs=OFF
             cmake --build build --target install
             cd ..

      - name: Build and Install leptonica
        shell: cmd
        run: |
             echo "Building leptonica..."
             git clone --depth 1 https://github.com/DanBloomberg/leptonica.git
             cd leptonica
             cmake -Bbuild -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_PREFIX_PATH=${{env.ILOC}} -DCMAKE_INSTALL_PREFIX=${{env.ILOC}} -DSW_BUILD=OFF -DBUILD_PROG=OFF -DBUILD_SHARED_LIBS=ON
             cmake --build build --target install

      - name: Remove not needed tools Before building tesseract
        shell: cmd
        run: >
             rm -Rf ${{env.ILOC}}/bin/*.exe

      - name: Build and Install tesseract
        shell: cmd
        run: |
             cmake -Bbuild -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_PREFIX_PATH=${{env.ILOC}} -DCMAKE_INSTALL_PREFIX=${{env.ILOC}} -DSW_BUILD=OFF -DBUILD_SHARED_LIBS=ON -DENABLE_LTO=ON -DBUILD_TRAINING_TOOLS=OFF -DFAST_FLOAT=ON -DGRAPHICS_DISABLED=ON -DOPENMP_BUILD=OFF
             cmake --build build --target install

      - name: Upload Build Results
        uses: actions/upload-artifact@v4
        with:
          name: tesseract-${{ steps.get_version.outputs.version }}-${{steps.get_version.outputs.stamp}}-VS2019_win64
          path: ${{env.ILOC}}
          retention-days: 5

      - name: Display Tesseract Version and Test Command Line Usage
        shell: cmd
        run: |
          curl -sSL https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata --output ${{env.ILOC}}/share/tessdata/eng.traineddata
          curl -sSL https://github.com/tesseract-ocr/tessdata/raw/main/osd.traineddata --output ${{env.ILOC}}/share/tessdata/osd.traineddata
          echo "Setting TESSDATA_PREFIX..."
          set TESSDATA_PREFIX=${{env.ILOC}}/share/tessdata
          echo "Setting PATH..."
          set PATH=${{env.ILOC}}/bin;%PATH%
          echo "Checking installed tesseract version..."
          tesseract -v
          echo "Checking installed langs"
          tesseract --list-langs
          echo "Checking OCR process"
          tesseract test/testing/phototest.tif -
